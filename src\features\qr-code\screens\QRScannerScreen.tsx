import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  StatusBar,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { useFocusEffect } from 'expo-router';
import * as Haptics from 'expo-haptics';
import {
  Camera,
  useCameraDevice,
  useCameraPermission,
  useCodeScanner,
} from 'react-native-vision-camera';
import { SafeAreaView } from 'react-native-safe-area-context';
import { io, Socket } from 'socket.io-client';
import Toast from 'react-native-toast-message';
import { Button } from 'react-native-ui-lib';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';

// Modern color palette
const COLORS = {
  black: '#000000',
  white: '#FFFFFF',
  primary: '#00FF88',
  secondary: '#1A1A1A',
  accent: '#FF6B35',
  success: '#4CAF50',
  error: '#F44336',
  warning: '#FF9800',
  overlay: 'rgba(0, 0, 0, 0.7)',
  glass: 'rgba(255, 255, 255, 0.1)',
  glassDark: 'rgba(0, 0, 0, 0.8)',
};

const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
};

const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

const API_URL = 'http://103.162.21.146:5003';

const QRScannerScreen = () => {
  const [scanned, setScanned] = useState(false);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [socketStatus, setSocketStatus] = useState('Đang kết nối...');
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const isProcessing = useRef(false);

  // Vision Camera hooks
  const { hasPermission, requestPermission } = useCameraPermission();
  const device = useCameraDevice('back');

  // Code scanner for QR codes
  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],

    onCodeScanned: codes => {
      if (codes.length > 0 && !scanned) {
        handleBarCodeScanned(codes[0].value || '');
      }
    },
  });

  // Animation refs
  const scanFrameScale = useRef(new Animated.Value(1)).current;
  const scanLinePosition = useRef(new Animated.Value(0)).current;
  const cornerPulse = useRef(new Animated.Value(1)).current;
  const buttonScale = useRef(new Animated.Value(1)).current;
  const overlayOpacity = useRef(new Animated.Value(0)).current;

  const { width, height } = Dimensions.get('window');
  const scanFrameSize = width * 0.75;

  // Start animations
  useEffect(() => {
    // Entrance animations
    Animated.parallel([
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scanFrameScale, {
        toValue: 1,
        tension: 500,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();

    // Scanning line animation
    const startScanLineAnimation = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scanLinePosition, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scanLinePosition, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    };

    // Corner pulse animation
    const startCornerPulse = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(cornerPulse, {
            toValue: 1.1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(cornerPulse, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    };

    const scanTimer = setTimeout(startScanLineAnimation, 1000);
    const pulseTimer = setTimeout(startCornerPulse, 1000);

    return () => {
      clearTimeout(scanTimer);
      clearTimeout(pulseTimer);
    };
  }, []);

  const handlePickImage = async () => {
    if (isProcessingImage) return;

    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Lỗi', 'Cần quyền truy cập thư viện ảnh để chọn ảnh');
        return;
      }

      setIsProcessingImage(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;

        Alert.alert(
          'Thông báo',
          'Chức năng quét QR từ ảnh chưa được hỗ trợ với Vision Camera. Vui lòng sử dụng camera để quét trực tiếp.',
        );
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Lỗi', 'Không thể mở thư viện ảnh');
    } finally {
      setIsProcessingImage(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      isProcessing.current = false;
      setScanned(false);
      console.log(`Connecting to WebSocket at ${API_URL}`);
      const newSocket = io(API_URL, {
        transports: ['websocket'],
        reconnection: true,
        reconnectionAttempts: 5,
      });
      setSocket(newSocket);
      newSocket.on('connect', () => {
        console.log('✅ WebSocket connected!');
        setSocketStatus('✅ Đã kết nối');
      });
      newSocket.on('disconnect', () => {
        console.log('🔌 WebSocket disconnected.');
        setSocketStatus('🔌 Đã ngắt kết nối');
      });
      newSocket.on('connect_error', err => {
        setSocketStatus('❌ Lỗi kết nối');
        console.log('Lỗi kết nối WebSocket:', err.message);
      });

      return () => {
        console.log('Disconnecting WebSocket...');
        newSocket.disconnect();
      };
    }, []),
  );

  const handleBarCodeScanned = useCallback(
    (data: string) => {
      if (isProcessing.current) return;
      isProcessing.current = true;
      setScanned(true);

      if (!socket || !socket.connected) {
        Toast.show({
          type: 'error',
          text1: 'Lỗi kết nối',
          text2: 'Không thể gửi dữ liệu. Vui lòng thử lại.',
        });
        setTimeout(() => {
          setScanned(false);
          isProcessing.current = false;
        }, 2000);
        return;
      }

      Animated.sequence([
        Animated.timing(scanFrameScale, {
          toValue: 1.05,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scanFrameScale, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();

      let citizenData = null;
      const formatDate = (str: string) => {
        if (!str || str.length !== 8) return str;
        return `${str.slice(0, 2)}/${str.slice(2, 4)}/${str.slice(4)}`;
      };

      const getDay = (str: string) => {
        if (!str || str.length !== 8) return '';
        return str.slice(0, 2);
      };

      const getMonth = (str: string) => {
        if (!str || str.length !== 8) return '';
        return str.slice(2, 4);
      };

      const getYear = (str: string) => {
        if (!str || str.length !== 8) return '';
        return str.slice(4);
      };

      const parts = data.split('|');
      if (parts.length >= 7) {
        console.log('Received data:', parts);
        citizenData = {
          so_cccd: parts[0] || '',
          so_cmnd: parts[1] || '',
          ho_ten: parts[2] || '',
          ngay_sinh: formatDate(parts[3] || ''),
          ns_ngay: getDay(parts[3] || ''),
          ns_thang: getMonth(parts[3] || ''),
          ns_nam: getYear(parts[3] || ''),
          gioi_tinh: parts[4] || '',
          noi_cu_tru: parts[5] || '',
          ngay_cap: formatDate(parts[6] || ''),
          nc_ngay: getDay(parts[6] || ''),
          nc_thang: getMonth(parts[6] || ''),
          nc_nam: getYear(parts[6] || ''),
        };
      }

      if (citizenData) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        console.log('Sending data:', citizenData);
        // socket.emit('send_data', citizenData);

        Toast.show({
          type: 'success',
          text1: 'Đã gửi thành công!',
          text2: citizenData.ho_ten,
          visibilityTime: 1500,
        });
      } else {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        Toast.show({
          type: 'error',
          text1: 'Lỗi Dữ Liệu',
          text2: 'Mã QR không đúng định dạng CCCD.',
          visibilityTime: 2000,
        });
      }

      setTimeout(() => {
        setScanned(false);
        isProcessing.current = false;
      }, 2000);
    },
    [socket, scanFrameScale],
  );

  const handleSendFakeData = useCallback(() => {
    if (!socket || !socket.connected) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi kết nối',
        text2: 'Không thể gửi dữ liệu.',
      });
      return;
    }
    const fakeData = {
      so_cccd: '001234567890',
      so_cmnd: '987654321',
      ho_ten: 'NGUYEN VAN FAKE',
      ngay_sinh: '01/01/1990',
      ns_ngay: '01',
      ns_thang: '01',
      ns_nam: '1990',
      gioi_tinh: 'Nam',
      noi_cu_tru: '123 Đường ABC, Phường XYZ, Quận 1, TP. HCM',
      ngay_cap: '15/07/2021',
      ngay_cap_ngay: '15',
      ngay_cap_thang: '07',
      ngay_cap_nam: '2021',
    };
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    console.log('Sending fake data:', fakeData);
    socket.emit('send_data', fakeData);
    Toast.show({
      type: 'success',
      text1: 'Đã gửi dữ liệu mẫu!',
      text2: fakeData.ho_ten,
      visibilityTime: 1500,
    });
  }, [socket]);

  const handleButtonPress = (action: () => void) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Animated.sequence([
      Animated.timing(buttonScale, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(buttonScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    action();
  };

  if (!device) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size='large' color={COLORS.primary} />
        <Text style={styles.loadingText}>Đang tải Camera...</Text>
      </View>
    );
  }

  if (!hasPermission) {
    return (
      <View style={styles.permissionContainer}>
        <Ionicons name='camera-outline' size={80} color={COLORS.primary} />
        <Text style={styles.permissionTitle}>Cần quyền truy cập Camera</Text>
        <Text style={styles.permissionSubtitle}>
          Ứng dụng cần quyền truy cập camera để quét mã QR
        </Text>
        <TouchableOpacity
          style={styles.permissionButton}
          onPress={() => requestPermission()}
        >
          <Text style={styles.permissionButtonText}>Cấp quyền</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle='light-content' backgroundColor={COLORS.black} />
      <Camera
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={true}
        codeScanner={!scanned ? codeScanner : undefined}
        focusable
        zoom={1.5}
        enableZoomGesture={true}
      />
      {/* Modern Overlay */}
      <Animated.View style={[styles.overlay, { opacity: overlayOpacity }]}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Quét QR Ver2</Text>
            <View style={styles.statusIndicator}>
              <View
                style={[
                  styles.statusDot,
                  {
                    backgroundColor: socketStatus.includes('✅')
                      ? COLORS.success
                      : COLORS.warning,
                  },
                ]}
              />
              <Text style={styles.statusText}>{socketStatus}</Text>
            </View>
          </View>
        </View>

        {/* Scan Area */}
        <View style={styles.scanArea}>
          {/* Scan Frame */}
          <Animated.View
            style={[
              styles.scanFrame,
              {
                width: scanFrameSize,
                height: scanFrameSize,
                transform: [{ scale: scanFrameScale }],
              },
            ]}
          >
            {/* Scanning Line */}
            <Animated.View
              style={[
                styles.scanLine,
                {
                  transform: [
                    {
                      translateY: scanLinePosition.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, scanFrameSize - 4],
                      }),
                    },
                  ],
                },
              ]}
            />

            {/* Corner Indicators */}
            <Animated.View
              style={[
                styles.corner,
                styles.cornerTopLeft,
                { transform: [{ scale: cornerPulse }] },
              ]}
            />
            <Animated.View
              style={[
                styles.corner,
                styles.cornerTopRight,
                { transform: [{ scale: cornerPulse }] },
              ]}
            />
            <Animated.View
              style={[
                styles.corner,
                styles.cornerBottomLeft,
                { transform: [{ scale: cornerPulse }] },
              ]}
            />
            <Animated.View
              style={[
                styles.corner,
                styles.cornerBottomRight,
                { transform: [{ scale: cornerPulse }] },
              ]}
            />

            {/* Center Icon */}
            <View style={styles.centerIcon}>
              <Ionicons
                name='qr-code'
                size={60}
                color={COLORS.white}
                style={{ opacity: 0.1 }}
              />
            </View>
          </Animated.View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.instructionText}>
            Đặt mã QR vào khung hình để quét
          </Text>

          <Animated.View
            style={[
              styles.buttonContainer,
              { transform: [{ scale: buttonScale }] },
            ]}
          >
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleButtonPress(handleSendFakeData)}
              activeOpacity={0.8}
            >
              <Ionicons name='send' size={20} color={COLORS.white} />
              <Text style={styles.buttonText}>Gửi dữ liệu mẫu</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                isProcessingImage && styles.buttonDisabled,
              ]}
              onPress={() => handleButtonPress(handlePickImage)}
              activeOpacity={0.8}
              disabled={isProcessingImage}
            >
              <Ionicons name='images' size={20} color={COLORS.white} />
              <Text style={styles.buttonText}>
                {isProcessingImage ? 'Đang xử lý...' : 'Chọn từ thư viện'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.black,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.black,
  },
  loadingText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.lg,
    marginTop: SPACING.lg,
    fontWeight: '500',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.black,
    padding: SPACING.xl,
  },
  permissionTitle: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    marginTop: SPACING.lg,
    textAlign: 'center',
  },
  permissionSubtitle: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    textAlign: 'center',
    marginTop: SPACING.sm,
    opacity: 0.8,
    lineHeight: 24,
  },
  permissionButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.xl,
    borderRadius: 12,
    marginTop: SPACING.xl,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  permissionButtonText: {
    color: COLORS.black,
    fontSize: FONT_SIZES.md,
    fontWeight: 'bold',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
  },
  header: {
    paddingTop: Platform.OS === 'ios' ? SPACING.xl : SPACING.lg,
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
  },
  headerContent: {
    alignItems: 'center',
    paddingTop: 20,
  },
  headerTitle: {
    color: COLORS.white,
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    marginBottom: SPACING.sm,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.glassDark,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.glass,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: SPACING.sm,
  },
  statusText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },

  scanFrame: {
    borderWidth: 2,
    borderColor: COLORS.primary,
    position: 'relative',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 10,
    elevation: 15,
  },
  scanLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: COLORS.primary,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 8,
  },
  corner: {
    position: 'absolute',
    width: 30,
    height: 30,
    borderColor: COLORS.primary,
    borderWidth: 4,
  },
  cornerTopLeft: {
    top: -2,
    left: -2,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 8,
  },
  cornerTopRight: {
    top: -2,
    right: -2,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 8,
  },
  cornerBottomLeft: {
    bottom: -2,
    left: -2,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 8,
  },
  cornerBottomRight: {
    bottom: -2,
    right: -2,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 8,
  },
  centerIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -30 }, { translateY: -30 }],
  },
  footer: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: Platform.OS === 'ios' ? SPACING.xl + 20 : SPACING.xl,
    alignItems: 'center',
  },
  instructionText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.md,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    fontWeight: '500',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    gap: SPACING.md,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.glassDark,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.glass,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  buttonText: {
    color: COLORS.white,
    fontSize: FONT_SIZES.sm,
    fontWeight: '600',
    marginLeft: SPACING.sm,
  },
});

export default QRScannerScreen;
